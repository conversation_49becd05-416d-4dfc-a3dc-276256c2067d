# 智能网页总结助手 - 安装与使用指南

## 📋 安装前准备

### 系统要求
- Chrome 88+ 或其他基于Chromium的浏览器（Edge、Opera等）
- 通义千问API密钥（从阿里云百炼平台获取）

### 获取API密钥

1. **注册阿里云账户**
   - 访问 [阿里云官网](https://www.aliyun.com/)
   - 注册并完成实名认证

2. **开通百炼服务**
   - 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
   - 开通百炼服务（可能需要充值）

3. **获取API密钥**
   - 在百炼控制台中创建应用
   - 获取API Key（格式类似：sk-xxxxxxxxxx）
   - 记录API密钥，稍后配置时需要使用

## 🚀 安装步骤

### 方法一：从源码安装（推荐）

1. **下载源码**
   ```bash
   git clone https://github.com/your-repo/智能网页总结助手.git
   cd 智能网页总结助手
   ```

2. **生成图标文件**
   - 在浏览器中打开 `generate-icons.html`
   - 依次下载16x16、32x32、48x48、128x128尺寸的图标
   - 将下载的图标文件重命名并放入 `assets/icons/` 目录

3. **加载扩展到Chrome**
   - 打开Chrome浏览器
   - 在地址栏输入 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录
   - 扩展安装成功后会出现在工具栏中

### 方法二：从发布包安装

1. **下载发布包**
   - 从GitHub Releases页面下载最新版本的.zip文件
   - 解压到本地目录

2. **安装步骤**
   - 按照方法一的步骤3进行安装

## ⚙️ 初始配置

### 1. 配置API密钥

1. **打开设置页面**
   - 点击扩展图标打开侧边栏
   - 点击右上角的设置按钮（齿轮图标）
   - 或者右键扩展图标选择"选项"

2. **配置API信息**
   - 在"API配置"标签页中：
     - API提供商：选择"通义千问"
     - API密钥：输入您的API密钥
     - API端点：保持默认值 `https://dashscope.aliyuncs.com/compatible-mode/v1`
     - 模型：推荐选择 `qwen-plus`

3. **测试连接**
   - 点击"测试连接"按钮
   - 如果显示"连接测试成功"，说明配置正确
   - 点击"保存配置"

### 2. 自定义设置（可选）

1. **界面设置**
   - 主题：选择深色或浅色主题
   - 侧边栏宽度：调整到合适的宽度
   - 功能开关：根据需要开启或关闭

2. **提示词模板**
   - 使用内置模板或创建自定义模板
   - 支持变量占位符 `{content}`

## 📖 使用方法

### 基本使用流程

1. **打开网页**
   - 访问任何您想要总结的网页
   - 确保页面内容已完全加载

2. **启动总结**
   - 点击浏览器工具栏中的扩展图标
   - 侧边栏会在页面右侧打开
   - 选择合适的总结模板
   - 点击"总结当前页面"按钮

3. **查看结果**
   - 等待AI生成总结（通常需要5-15秒）
   - 查看格式化的总结结果
   - 使用复制或导出功能保存结果

### 快捷键操作

- `Ctrl + Enter`：快速开始总结
- `Ctrl + C`：复制总结结果（当结果可见时）
- `Escape`：关闭对话框

### 高级功能

1. **多格式导出**
   - 点击导出按钮选择格式
   - 支持TXT、Markdown、HTML、JSON格式
   - 可选择是否包含页面信息

2. **自定义提示词**
   - 在设置页面创建专属模板
   - 使用 `{content}` 占位符
   - 支持预览和测试

3. **历史记录**
   - 自动保存总结历史
   - 在高级设置中管理历史记录

## 🔧 故障排除

### 常见问题

**Q: 扩展无法加载？**
A: 
- 检查manifest.json语法是否正确
- 确保所有文件路径存在
- 查看Chrome扩展页面的错误信息

**Q: API调用失败？**
A:
- 验证API密钥是否正确
- 检查网络连接
- 确认API账户余额充足
- 查看控制台错误信息

**Q: 内容提取不准确？**
A:
- 刷新页面后重试
- 检查页面是否完全加载
- 某些动态网站可能需要等待内容加载

**Q: 总结质量不佳？**
A:
- 尝试不同的提示词模板
- 调整API参数（温度值等）
- 确保提取的内容质量良好

### 调试方法

1. **查看控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **检查扩展状态**
   - 访问 `chrome://extensions/`
   - 查看扩展是否正常启用
   - 点击"详细信息"查看权限

3. **重新加载扩展**
   - 在扩展页面点击刷新按钮
   - 或者禁用后重新启用扩展

## 📊 性能优化

### 提高使用体验

1. **网络优化**
   - 使用稳定的网络连接
   - 避免在网络繁忙时使用

2. **内容优化**
   - 等待页面完全加载后再总结
   - 避免总结过长的内容（超过10000字）

3. **API优化**
   - 合理设置API参数
   - 避免频繁调用API

## 🔄 更新升级

### 检查更新

1. **手动检查**
   - 访问项目GitHub页面
   - 查看是否有新版本发布

2. **更新步骤**
   - 下载新版本文件
   - 替换旧版本文件
   - 在扩展页面点击刷新

### 数据迁移

- 配置数据会自动保留
- 建议在更新前导出配置备份

## 📞 技术支持

### 获取帮助

- **GitHub Issues**: 报告bug或请求功能
- **文档**: 查看README.md获取详细信息
- **社区**: 参与讨论和交流

### 贡献代码

- Fork项目仓库
- 创建功能分支
- 提交Pull Request

---

**祝您使用愉快！** 🎉

如果遇到任何问题，请不要犹豫联系我们。
