<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试硬编码API配置</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .config-item {
            margin-bottom: 15px;
        }
        label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试硬编码API配置</h1>
        
        <div class="config-section">
            <h2>当前API配置</h2>
            <div class="config-item">
                <label>API密钥:</label>
                <input type="password" id="apiKey" readonly>
                <button onclick="toggleApiKey()">显示/隐藏</button>
            </div>
            <div class="config-item">
                <label>API端点:</label>
                <input type="text" id="baseUrl" readonly>
            </div>
            <div class="config-item">
                <label>模型:</label>
                <input type="text" id="model" readonly>
            </div>
        </div>
        
        <div class="config-section">
            <h2>API测试</h2>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="testSummarization()">测试总结功能</button>
            <div id="result"></div>
        </div>
    </div>

    <script>
        // 硬编码的API配置（与扩展中相同）
        const HARDCODED_API_CONFIG = {
            apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4',
            baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
            model: 'qwen-plus'
        };

        let isShowingFullKey = false;

        // 格式化API密钥显示
        function formatApiKeyForDisplay(apiKey) {
            if (!apiKey || apiKey.length < 8) {
                return apiKey;
            }
            
            const start = apiKey.substring(0, 8);
            const end = apiKey.substring(apiKey.length - 4);
            const middle = '*'.repeat(Math.max(0, apiKey.length - 12));
            
            return `${start}${middle}${end}`;
        }

        // 初始化页面
        function initPage() {
            document.getElementById('apiKey').value = formatApiKeyForDisplay(HARDCODED_API_CONFIG.apiKey);
            document.getElementById('baseUrl').value = HARDCODED_API_CONFIG.baseUrl;
            document.getElementById('model').value = HARDCODED_API_CONFIG.model;
        }

        // 切换API密钥显示
        function toggleApiKey() {
            const apiKeyInput = document.getElementById('apiKey');
            
            if (isShowingFullKey) {
                apiKeyInput.value = formatApiKeyForDisplay(HARDCODED_API_CONFIG.apiKey);
                apiKeyInput.type = 'password';
            } else {
                apiKeyInput.value = HARDCODED_API_CONFIG.apiKey;
                apiKeyInput.type = 'text';
            }
            
            isShowingFullKey = !isShowingFullKey;
        }

        // 显示结果
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        // 测试API连接
        async function testAPI() {
            showResult('正在测试API连接...', 'loading');
            
            try {
                const response = await fetch(`${HARDCODED_API_CONFIG.baseUrl}/models`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${HARDCODED_API_CONFIG.apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult(`API连接成功！\n状态码: ${response.status}\n可用模型数量: ${data.data ? data.data.length : '未知'}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`API连接失败！\n状态码: ${response.status}\n错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`API连接失败！\n错误: ${error.message}`, 'error');
            }
        }

        // 测试总结功能
        async function testSummarization() {
            showResult('正在测试总结功能...', 'loading');
            
            const testContent = '这是一个测试文档。人工智能（AI）是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。';
            
            const messages = [
                {
                    role: 'system',
                    content: '你是一个专业的内容总结助手，能够准确提取和总结各种类型文档的核心信息。请用中文回答。'
                },
                {
                    role: 'user',
                    content: `请对以下内容进行总结，提取关键信息和要点：\n\n${testContent}`
                }
            ];

            try {
                const response = await fetch(`${HARDCODED_API_CONFIG.baseUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${HARDCODED_API_CONFIG.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: HARDCODED_API_CONFIG.model,
                        messages: messages,
                        temperature: 0.7,
                        max_tokens: 2000
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const summary = data.choices[0].message.content;
                    showResult(`总结功能测试成功！\n\n原文: ${testContent}\n\n总结: ${summary}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`总结功能测试失败！\n状态码: ${response.status}\n错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`总结功能测试失败！\n错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', initPage);
    </script>
</body>
</html>
