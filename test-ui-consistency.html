<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI一致性测试 - 智能网页总结助手</title>
    <link rel="stylesheet" href="sidebar/sidebar.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
        }
        
        .test-section {
            margin-bottom: var(--spacing-xl);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            text-align: center;
        }
        
        .button-comparison {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .button-info {
            background: var(--bg-card);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }
        
        .button-info h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-accent);
            font-size: 14px;
        }
        
        .button-info p {
            margin: 0;
            font-size: 12px;
            color: var(--text-muted);
        }
        
        .theme-toggle {
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .theme-btn {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 14px;
        }
        
        .loading-demo {
            margin-top: var(--spacing-md);
        }
        
        .demo-controls {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .demo-btn {
            flex: 1;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            padding: var(--spacing-xs);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="theme-toggle">
            <button class="theme-btn" onclick="toggleTheme()">切换主题</button>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">按钮一致性测试</h2>
            
            <div class="button-info">
                <h4>主要按钮 (primary-btn)</h4>
                <p>用于"总结当前页面"功能</p>
            </div>
            
            <button class="primary-btn" id="primaryBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                    <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
                <span>总结当前页面</span>
            </button>
            
            <div class="button-info">
                <h4>次要按钮 (secondary-btn)</h4>
                <p>用于"提取Markdown"功能 - 已更新图标和样式</p>
            </div>
            
            <button class="secondary-btn" id="secondaryBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2"/>
                    <path d="M7 15V9l2 2 2-2v6" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M17 11l-2 2 2 2" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
                <span>提取Markdown</span>
            </button>
        </div>
        
        <div class="test-section">
            <h3 class="test-title">加载状态测试</h3>
            <div class="demo-controls">
                <button class="demo-btn" onclick="toggleLoading('primaryBtn')">主按钮加载</button>
                <button class="demo-btn" onclick="toggleLoading('secondaryBtn')">次按钮加载</button>
                <button class="demo-btn" onclick="clearLoading()">清除加载</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="test-title">样式对比</h3>
            <div class="button-info">
                <h4>统一的样式属性：</h4>
                <p>• 相同的内边距：16px 24px</p>
                <p>• 相同的字体大小：16px</p>
                <p>• 相同的字体粗细：600</p>
                <p>• 相同的边框圆角：12px</p>
                <p>• 相同的图标大小：20x20px</p>
                <p>• 统一的悬停效果和过渡动画</p>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? null : 'light';
            body.setAttribute('data-theme', newTheme || '');
        }
        
        function toggleLoading(buttonId) {
            const button = document.getElementById(buttonId);
            button.classList.toggle('loading');
        }
        
        function clearLoading() {
            document.getElementById('primaryBtn').classList.remove('loading');
            document.getElementById('secondaryBtn').classList.remove('loading');
        }
        
        // 添加按钮点击效果演示
        document.querySelectorAll('.primary-btn, .secondary-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!this.classList.contains('loading')) {
                    console.log(`点击了: ${this.textContent.trim()}`);
                }
            });
        });
    </script>
</body>
</html>
