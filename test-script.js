// 智能网页总结助手 - Markdown提取功能测试脚本
// 在浏览器控制台中运行此脚本来测试功能

console.log('🚀 开始测试智能网页总结助手 - Markdown提取功能');

// 测试配置
const TEST_CONFIG = {
    apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4', // 从docs/env.md读取
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    model: 'qwen-plus'
};

// 测试用例
const TEST_CASES = [
    {
        name: '短文本测试',
        content: '这是一个简单的测试文本。包含一些基本的内容，用于验证Markdown提取功能是否正常工作。',
        expected: '应该能够正常提取并转换为Markdown格式'
    },
    {
        name: '带标题的文本测试',
        content: `
# 主标题
这是主要内容。

## 二级标题
这是二级标题下的内容。

### 三级标题
这是三级标题下的内容。

- 列表项1
- 列表项2
- 列表项3

**粗体文本** 和 *斜体文本*
        `,
        expected: '应该保持标题层次和格式'
    }
];

// 测试函数
class MarkdownExtractorTester {
    constructor() {
        this.results = [];
        this.markdownParser = null;
        
        // 尝试加载MarkdownParser
        if (typeof MarkdownParser !== 'undefined') {
            this.markdownParser = new MarkdownParser();
            console.log('✅ MarkdownParser 加载成功');
        } else {
            console.warn('⚠️ MarkdownParser 未加载，将跳过解析器测试');
        }
    }

    // 测试Markdown解析器
    testMarkdownParser() {
        if (!this.markdownParser) {
            console.log('❌ 跳过Markdown解析器测试（未加载）');
            return;
        }

        console.log('\n📝 测试Markdown解析器...');
        
        TEST_CASES.forEach((testCase, index) => {
            try {
                console.log(`\n测试用例 ${index + 1}: ${testCase.name}`);
                
                const result = this.markdownParser.parseWithTOC(testCase.content);
                
                console.log('输入:', testCase.content.substring(0, 100) + '...');
                console.log('HTML输出:', result.html.substring(0, 200) + '...');
                console.log('目录:', result.toc ? '已生成' : '无');
                console.log('字数:', result.wordCount);
                
                this.results.push({
                    test: testCase.name,
                    status: 'passed',
                    result: result
                });
                
                console.log('✅ 测试通过');
            } catch (error) {
                console.error('❌ 测试失败:', error);
                this.results.push({
                    test: testCase.name,
                    status: 'failed',
                    error: error.message
                });
            }
        });
    }

    // 测试扩展消息传递
    async testExtensionMessaging() {
        console.log('\n📡 测试扩展消息传递...');
        
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            console.log('❌ Chrome扩展API不可用（请在扩展环境中运行）');
            return;
        }

        try {
            // 测试获取配置
            const configResponse = await this.sendMessage({ action: 'getConfig' });
            console.log('配置获取:', configResponse.success ? '✅ 成功' : '❌ 失败');
            
            if (configResponse.success) {
                console.log('API配置:', configResponse.config.apiConfig ? '已配置' : '未配置');
            }

            // 测试内容提取
            const extractResponse = await this.sendMessage({ action: 'extractContent' });
            console.log('内容提取:', extractResponse.success ? '✅ 成功' : '❌ 失败');
            
            if (extractResponse.success) {
                console.log('页面标题:', extractResponse.title);
                console.log('内容长度:', extractResponse.content.content.length);
            }

        } catch (error) {
            console.error('❌ 消息传递测试失败:', error);
        }
    }

    // 发送消息到扩展
    sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 测试UI元素
    testUIElements() {
        console.log('\n🎨 测试UI元素...');
        
        const elements = [
            'markdownBtn',
            'markdownSection',
            'markdownPreview',
            'markdownSource',
            'markdownTextarea',
            'markdownCopyBtn',
            'markdownDownloadBtn',
            'previewTab',
            'sourceTab'
        ];

        elements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                console.log(`✅ ${elementId} 元素存在`);
            } else {
                console.log(`❌ ${elementId} 元素缺失`);
            }
        });
    }

    // 测试CSS样式
    testCSSStyles() {
        console.log('\n🎨 测试CSS样式...');
        
        const requiredClasses = [
            'markdown-section',
            'markdown-header',
            'markdown-tabs',
            'tab-btn',
            'markdown-content',
            'markdown-source',
            'markdown-meta'
        ];

        requiredClasses.forEach(className => {
            const elements = document.getElementsByClassName(className);
            if (elements.length > 0) {
                console.log(`✅ .${className} 样式类存在`);
            } else {
                console.log(`❌ .${className} 样式类缺失`);
            }
        });
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🧪 开始运行所有测试...\n');
        
        this.testMarkdownParser();
        await this.testExtensionMessaging();
        this.testUIElements();
        this.testCSSStyles();
        
        console.log('\n📊 测试结果汇总:');
        console.log('通过的测试:', this.results.filter(r => r.status === 'passed').length);
        console.log('失败的测试:', this.results.filter(r => r.status === 'failed').length);
        
        if (this.results.some(r => r.status === 'failed')) {
            console.log('\n❌ 失败的测试:');
            this.results.filter(r => r.status === 'failed').forEach(r => {
                console.log(`- ${r.test}: ${r.error}`);
            });
        }
        
        console.log('\n🎉 测试完成！');
    }
}

// 自动运行测试
if (typeof window !== 'undefined') {
    window.runMarkdownTests = () => {
        const tester = new MarkdownExtractorTester();
        tester.runAllTests();
    };
    
    // 如果在扩展环境中，自动运行测试
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        setTimeout(() => {
            console.log('🔄 自动运行测试（3秒后开始）...');
            setTimeout(() => {
                window.runMarkdownTests();
            }, 3000);
        }, 1000);
    } else {
        console.log('💡 在扩展环境中运行 runMarkdownTests() 来开始测试');
    }
}

// 导出测试类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarkdownExtractorTester;
}
