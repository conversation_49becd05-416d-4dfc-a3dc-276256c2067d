# 智能网页总结助手 - Markdown提取功能

## 功能概述

智能网页总结助手现已支持**一键Markdown提取**功能，能够将网页内容智能转换为标准的Markdown格式。该功能基于通义千问qwen-plus模型，提供高质量的内容提取和格式转换。

## 核心特性

### 🤖 AI智能提取
- 使用qwen-plus模型进行内容分析
- 智能识别文章结构和重要信息
- 自动过滤广告、导航等无关内容
- 保持原文的逻辑层次和格式

### 📱 实时预览
- 提供Markdown内容的可视化预览
- 支持预览和源码编辑模式切换
- 自动生成文章目录（TOC）
- 支持目录跳转功能

### ⚡ 智能分块处理
- 自动检测长文章并分块处理
- 避免API token限制问题
- 智能合并处理结果
- 保持内容完整性

### 🎯 多种操作选项
- 一键复制到剪贴板
- 下载为.md文件
- 在线编辑Markdown内容
- 自动保存到历史记录

### 🛡️ 完善的用户体验
- 详细的进度指示
- 支持取消正在进行的操作
- 友好的错误提示和处理
- 长文章处理前的确认提示

## 安装配置

### 1. API配置
在使用Markdown提取功能前，请确保已配置API密钥：

1. 点击扩展设置按钮
2. 在API配置中填入通义千问API密钥
3. 确认API地址为：`https://dashscope.aliyuncs.com/compatible-mode/v1`
4. 选择模型：`qwen-plus`

### 2. 环境要求
- Chrome浏览器版本 88+
- 有效的通义千问API密钥
- 稳定的网络连接

## 使用方法

### 基本使用流程

1. **打开扩展**
   - 点击浏览器工具栏中的扩展图标
   - 或使用快捷键打开侧边栏

2. **选择功能**
   - 在侧边栏中找到"提取Markdown"按钮
   - 按钮位于"总结当前页面"按钮下方

3. **开始提取**
   - 点击"提取Markdown"按钮
   - 系统会自动分析当前页面内容
   - 对于长文章，会显示确认对话框

4. **查看结果**
   - 等待AI处理完成
   - 在预览区域查看转换结果
   - 可切换到源码模式进行编辑

5. **执行操作**
   - 点击复制按钮将内容复制到剪贴板
   - 点击下载按钮保存为.md文件
   - 可在源码模式下手动编辑内容

### 高级功能

#### 目录生成
系统会自动为包含标题的文章生成目录：
- 支持1-6级标题
- 自动生成锚点链接
- 点击目录项可快速跳转

#### 分块处理
对于超过6000字符的长文章：
- 系统会自动提示用户确认
- 智能分割内容避免API限制
- 自动合并处理结果
- 显示处理块数信息

#### 取消操作
在处理过程中可随时取消：
- 点击加载界面中的"取消操作"按钮
- 或按ESC键取消
- 系统会安全停止当前操作

## 技术实现

### 架构设计
```
用户界面 (sidebar.html/js)
    ↓
消息传递 (Chrome Extension API)
    ↓
后台处理 (background.js)
    ↓
AI API调用 (qwen-plus)
    ↓
结果处理和展示
```

### 核心组件

1. **内容提取器**
   - 智能识别主要内容区域
   - 过滤无关元素
   - 保持文本结构

2. **Markdown转换器**
   - AI驱动的格式转换
   - 保持原文层次结构
   - 优化输出格式

3. **预览渲染器**
   - 实时Markdown渲染
   - 目录生成和跳转
   - 响应式设计

4. **文件操作器**
   - 剪贴板集成
   - 文件下载功能
   - 历史记录管理

### 性能优化

- **智能缓存**：避免重复处理相同内容
- **分块算法**：优化长文章处理效率
- **异步处理**：保持界面响应性
- **错误重试**：提高成功率

## 兼容性说明

### 与现有功能的兼容性
- ✅ 完全兼容现有的网页总结功能
- ✅ 共享相同的API配置
- ✅ 统一的历史记录管理
- ✅ 一致的用户界面风格

### 浏览器兼容性
- ✅ Chrome 88+
- ✅ Edge 88+
- ✅ 其他Chromium内核浏览器

### 网站兼容性
- ✅ 支持大部分静态网页
- ✅ 支持动态加载的内容
- ⚠️ 部分SPA应用可能需要等待内容加载完成
- ❌ 不支持需要登录的受保护内容

## 故障排除

### 常见问题

**Q: 提取失败，显示"API密钥无效"**
A: 请检查API密钥配置是否正确，确保密钥有效且有足够余额。

**Q: 长文章处理时间过长**
A: 这是正常现象，长文章需要分块处理。可以点击取消按钮停止操作。

**Q: 提取的内容不完整**
A: 可能是页面内容还未完全加载，请等待页面加载完成后重试。

**Q: 预览显示异常**
A: 可以切换到源码模式查看原始Markdown内容，或手动编辑修正。

### 错误代码

- `401`: API密钥无效
- `429`: API调用频率超限
- `500`: API服务器错误
- `NETWORK_ERROR`: 网络连接问题

## 更新日志

### v1.1.0 (当前版本)
- ✨ 新增一键Markdown提取功能
- 🎨 优化用户界面和交互体验
- ⚡ 实现智能分块处理算法
- 🔧 添加实时预览和编辑功能
- 🛡️ 增强错误处理和用户反馈
- 📱 改进响应式设计
- 🎯 添加目录生成功能

## 反馈与支持

如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式反馈：

- 在扩展商店中留言评价
- 通过扩展设置页面的反馈功能
- 发送邮件至技术支持

感谢您使用智能网页总结助手！
