<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试扩展功能 - 智能网页总结助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .article {
            max-width: 800px;
            margin: 0 auto;
        }
        .article h2 {
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .article h3 {
            color: #555;
            margin-top: 30px;
        }
        .article p {
            margin-bottom: 20px;
            text-align: justify;
        }
        .highlight {
            background: #fff3cd;
            padding: 20px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .instructions {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #17a2b8;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0c5460;
        }
        .instructions ol {
            margin-bottom: 0;
        }
        .instructions li {
            margin-bottom: 8px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tech-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .spec-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .spec-card h4 {
            margin-top: 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 智能网页总结助手测试页面</h1>
        <p>这是一个专门用于测试Chrome扩展功能的示例页面</p>
    </div>

    <div class="instructions">
        <h3>📋 测试说明</h3>
        <ol>
            <li>确保已安装"智能网页总结助手"Chrome扩展</li>
            <li>点击浏览器工具栏中的扩展图标，或使用快捷键打开侧边栏</li>
            <li>在侧边栏中点击"开始总结"按钮，测试AI总结功能</li>
            <li>点击"提取Markdown"按钮，测试Markdown提取功能</li>
            <li>在扩展设置页面中查看API配置是否正确显示</li>
        </ol>
    </div>

    <div class="content">
        <article class="article">
            <h2>人工智能的发展历程与未来展望</h2>
            
            <p>人工智能（Artificial Intelligence，简称AI）作为计算机科学的一个重要分支，自20世纪50年代诞生以来，经历了多次发展浪潮。从最初的符号主义方法到现在的深度学习技术，AI技术不断演进，正在深刻改变着我们的生活和工作方式。</p>

            <h3>发展历程回顾</h3>
            
            <p>1956年，达特茅斯会议标志着人工智能学科的正式诞生。在这次会议上，约翰·麦卡锡首次提出了"人工智能"这一概念。早期的AI研究主要集中在符号推理、专家系统等领域，试图通过逻辑规则来模拟人类的思维过程。</p>

            <div class="highlight">
                <strong>重要里程碑：</strong>1997年，IBM的深蓝计算机击败了国际象棋世界冠军加里·卡斯帕罗夫，这一事件标志着AI在特定领域开始超越人类专家水平。
            </div>

            <p>进入21世纪后，随着计算能力的提升和大数据的涌现，机器学习特别是深度学习技术取得了突破性进展。2012年，AlexNet在ImageNet图像识别竞赛中的优异表现，开启了深度学习的新时代。</p>

            <h3>当前技术现状</h3>

            <div class="tech-specs">
                <div class="spec-card">
                    <h4>🧠 深度学习</h4>
                    <p>基于神经网络的深度学习技术在图像识别、自然语言处理、语音识别等领域取得了显著成果，准确率不断提升。</p>
                </div>
                <div class="spec-card">
                    <h4>💬 大语言模型</h4>
                    <p>GPT、BERT等大型语言模型展现了强大的文本理解和生成能力，为自然语言处理带来了革命性变化。</p>
                </div>
                <div class="spec-card">
                    <h4>🤖 多模态AI</h4>
                    <p>能够同时处理文本、图像、音频等多种数据类型的AI系统，为更复杂的应用场景提供了可能。</p>
                </div>
                <div class="spec-card">
                    <h4>🔄 强化学习</h4>
                    <p>通过与环境交互学习最优策略的强化学习技术，在游戏、机器人控制等领域表现出色。</p>
                </div>
            </div>

            <h3>应用领域拓展</h3>

            <p>现代AI技术已经广泛应用于各个行业和领域：</p>

            <p><strong>医疗健康：</strong>AI辅助诊断系统能够帮助医生更准确地识别疾病，药物发现过程也因AI技术而大大加速。在影像诊断、病理分析、个性化治疗方案制定等方面，AI都展现出了巨大潜力。</p>

            <p><strong>自动驾驶：</strong>通过计算机视觉、传感器融合和决策算法，自动驾驶技术正在逐步走向成熟。特斯拉、Waymo等公司在这一领域投入巨大，推动着交通出行方式的变革。</p>

            <p><strong>金融科技：</strong>AI在风险评估、算法交易、反欺诈检测、智能客服等金融服务领域发挥着重要作用，提高了服务效率和安全性。</p>

            <p><strong>教育培训：</strong>个性化学习系统能够根据学生的学习特点和进度，提供定制化的教学内容和学习路径，提升教育效果。</p>

            <h3>未来发展趋势</h3>

            <p>展望未来，人工智能技术将在以下几个方向继续发展：</p>

            <p><strong>通用人工智能（AGI）：</strong>虽然目前的AI系统在特定任务上表现优异，但距离真正的通用智能还有很长的路要走。研究者们正在探索如何构建能够在多个领域都具备人类水平智能的系统。</p>

            <p><strong>可解释AI：</strong>随着AI系统在关键决策中的应用增加，提高AI决策过程的透明度和可解释性变得越来越重要。这不仅有助于建立用户信任，也是监管合规的需要。</p>

            <p><strong>边缘计算与AI：</strong>将AI计算能力部署到边缘设备上，能够减少延迟、保护隐私，并降低对云端计算资源的依赖。这对于物联网、移动设备等应用场景具有重要意义。</p>

            <div class="highlight">
                <strong>思考：</strong>人工智能技术的快速发展也带来了新的挑战，包括就业影响、隐私保护、算法偏见、安全风险等问题。如何在推动技术进步的同时，确保AI技术的安全、公平和可持续发展，是全社会需要共同面对的重要课题。
            </div>

            <h3>结语</h3>

            <p>人工智能正在从科幻概念转变为现实应用，深刻影响着社会的各个方面。虽然我们距离真正的通用人工智能还有很长的路要走，但当前的技术进展已经为我们展示了AI的巨大潜力。</p>

            <p>在这个快速变化的时代，我们需要保持开放的心态，积极拥抱AI技术带来的机遇，同时也要理性看待其局限性和风险。只有这样，我们才能更好地利用AI技术，创造一个更加智能、高效和美好的未来。</p>
        </article>
    </div>

    <div class="footer">
        <p>🔧 这是一个测试页面，用于验证智能网页总结助手的功能</p>
        <p>请使用Chrome扩展对本页面进行总结和Markdown提取测试</p>
    </div>
</body>
</html>
