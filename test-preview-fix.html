<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预览功能修复测试 - 智能网页总结助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .test-instructions {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #17a2b8;
        }
        .test-instructions h3 {
            margin-top: 0;
            color: #0c5460;
        }
        .long-content {
            margin: 30px 0;
        }
        .long-content h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .long-content h3 {
            color: #555;
            margin-top: 25px;
        }
        .long-content p {
            margin-bottom: 15px;
            text-align: justify;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .code-block {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }
        .list-section {
            margin: 20px 0;
        }
        .list-section ul, .list-section ol {
            margin-left: 20px;
        }
        .list-section li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 预览功能修复测试页面</h1>
        <p>测试Chrome扩展预览功能的显示完整性和滚动功能</p>
    </div>

    <div class="test-instructions">
        <h3>📋 测试说明</h3>
        <ol>
            <li>确保已安装修复后的"智能网页总结助手"Chrome扩展</li>
            <li>点击扩展图标打开侧边栏</li>
            <li>点击"开始总结"按钮，测试总结结果的完整显示</li>
            <li>点击"提取Markdown"按钮，测试Markdown预览的完整显示</li>
            <li>点击"👁️ 独立窗口预览"按钮，测试预览窗口的显示效果</li>
            <li>验证滚动功能是否正常工作</li>
        </ol>
    </div>

    <div class="content">
        <div class="long-content">
            <h2>人工智能技术发展的深度分析与未来展望</h2>
            
            <h3>1. 引言</h3>
            <p>人工智能（Artificial Intelligence，AI）作为21世纪最具革命性的技术之一，正在深刻改变着我们的生活方式、工作模式和社会结构。从最初的概念提出到如今的广泛应用，AI技术经历了多次发展浪潮，每一次都带来了前所未有的机遇和挑战。</p>

            <p>本文将从多个维度深入分析人工智能技术的发展历程、当前状况、应用领域、技术挑战以及未来发展趋势，为读者提供一个全面而深入的AI技术发展图景。</p>

            <h3>2. 人工智能发展历程回顾</h3>
            
            <h4>2.1 起源阶段（1950-1970年代）</h4>
            <p>人工智能的概念最早可以追溯到1950年，当时英国数学家阿兰·图灵提出了著名的"图灵测试"，为判断机器是否具有智能提供了一个标准。1956年，达特茅斯会议的召开标志着人工智能学科的正式诞生，约翰·麦卡锡在这次会议上首次提出了"人工智能"这一术语。</p>

            <p>在这一阶段，研究者们主要关注符号推理和问题求解，开发了一些早期的AI程序，如逻辑理论机（Logic Theorist）和通用问题求解器（General Problem Solver）。虽然这些早期尝试取得了一定成果，但由于计算能力的限制和对问题复杂性认识的不足，AI技术的发展相对缓慢。</p>

            <h4>2.2 专家系统时代（1970-1990年代）</h4>
            <p>20世纪70年代到90年代，专家系统成为AI研究的主流方向。专家系统通过将人类专家的知识和经验编码成规则库，使计算机能够在特定领域内进行推理和决策。这一时期诞生了许多成功的专家系统，如医疗诊断系统MYCIN和地质勘探系统PROSPECTOR。</p>

            <div class="highlight">
                <strong>重要里程碑：</strong>1997年，IBM的深蓝计算机击败了国际象棋世界冠军加里·卡斯帕罗夫，这一事件标志着AI在特定领域开始超越人类专家水平，引起了全世界的广泛关注。
            </div>

            <h4>2.3 机器学习兴起（1990-2010年代）</h4>
            <p>随着计算能力的提升和数据量的增长，机器学习逐渐成为AI研究的核心。这一时期，支持向量机、随机森林、神经网络等算法得到了广泛应用。统计学习理论的发展为机器学习提供了坚实的理论基础，使得AI系统能够从数据中自动学习模式和规律。</p>

            <h4>2.4 深度学习革命（2010年代至今）</h4>
            <p>2012年，AlexNet在ImageNet图像识别竞赛中的突破性表现开启了深度学习的新时代。深度神经网络在图像识别、自然语言处理、语音识别等领域取得了前所未有的成果，AI技术开始真正走向实用化和产业化。</p>

            <h3>3. 当前AI技术的主要分支</h3>

            <div class="list-section">
                <h4>3.1 机器学习</h4>
                <ul>
                    <li><strong>监督学习：</strong>通过标注数据训练模型，用于分类和回归任务</li>
                    <li><strong>无监督学习：</strong>从无标注数据中发现隐藏模式，如聚类和降维</li>
                    <li><strong>强化学习：</strong>通过与环境交互学习最优策略，在游戏和机器人控制中表现出色</li>
                    <li><strong>半监督学习：</strong>结合少量标注数据和大量无标注数据进行学习</li>
                </ul>

                <h4>3.2 深度学习</h4>
                <ul>
                    <li><strong>卷积神经网络（CNN）：</strong>在图像处理和计算机视觉领域占主导地位</li>
                    <li><strong>循环神经网络（RNN）：</strong>适用于序列数据处理，如时间序列分析和自然语言处理</li>
                    <li><strong>Transformer架构：</strong>革命性地改变了自然语言处理领域，催生了GPT、BERT等大型语言模型</li>
                    <li><strong>生成对抗网络（GAN）：</strong>在图像生成、数据增强等领域展现出强大能力</li>
                </ul>

                <h4>3.3 自然语言处理</h4>
                <ul>
                    <li><strong>文本理解：</strong>情感分析、文本分类、命名实体识别等</li>
                    <li><strong>文本生成：</strong>机器翻译、文本摘要、对话系统等</li>
                    <li><strong>大语言模型：</strong>GPT系列、BERT、T5等模型在多项NLP任务中达到人类水平</li>
                </ul>
            </div>

            <h3>4. AI技术的广泛应用</h3>

            <h4>4.1 医疗健康领域</h4>
            <p>AI在医疗领域的应用正在革命性地改变传统医疗模式。医学影像诊断是AI应用最成功的领域之一，深度学习算法在X光片、CT扫描、MRI图像的分析中表现出了超越人类医生的准确性。例如，Google的AI系统在糖尿病视网膜病变的诊断中达到了90%以上的准确率。</p>

            <p>药物发现是另一个AI技术大显身手的领域。传统的药物研发周期长达10-15年，成本高达数十亿美元。AI技术能够通过分析大量的分子数据，预测药物的效果和副作用，大大缩短研发周期。DeepMind的AlphaFold在蛋白质结构预测方面的突破，为新药研发开辟了新的道路。</p>

            <h4>4.2 自动驾驶技术</h4>
            <p>自动驾驶技术是AI技术集成应用的典型代表，涉及计算机视觉、传感器融合、路径规划、决策控制等多个技术领域。目前，特斯拉、Waymo、百度等公司在自动驾驶技术方面投入巨大，技术水平不断提升。</p>

            <div class="code-block">
自动驾驶技术栈：
├── 感知层
│   ├── 摄像头视觉处理
│   ├── 激光雷达点云处理
│   ├── 毫米波雷达信号处理
│   └── 传感器融合
├── 决策层
│   ├── 路径规划
│   ├── 行为预测
│   ├── 决策制定
│   └── 风险评估
└── 控制层
    ├── 转向控制
    ├── 速度控制
    ├── 制动控制
    └── 安全监控
            </div>

            <h4>4.3 金融科技</h4>
            <p>AI技术在金融领域的应用涵盖了风险管理、算法交易、反欺诈检测、智能客服等多个方面。机器学习算法能够分析海量的交易数据，识别异常模式，提高风险控制的精度和效率。</p>

            <p>量化交易是AI在金融领域的重要应用之一。通过分析历史数据、市场情绪、宏观经济指标等多维度信息，AI系统能够制定复杂的交易策略，在毫秒级别内执行交易决策。</p>

            <h4>4.4 教育技术</h4>
            <p>个性化学习是AI在教育领域最有前景的应用方向。通过分析学生的学习行为、知识掌握情况和学习偏好，AI系统能够为每个学生定制个性化的学习路径和内容推荐。</p>

            <p>智能辅导系统能够提供24/7的学习支持，回答学生的问题，提供即时反馈，帮助学生更好地理解和掌握知识。语言学习应用如Duolingo就是AI技术在教育领域成功应用的典型例子。</p>

            <h3>5. 技术挑战与限制</h3>

            <h4>5.1 数据质量与隐私保护</h4>
            <p>AI系统的性能很大程度上依赖于训练数据的质量和数量。然而，在实际应用中，数据往往存在噪声、偏差、不完整等问题。同时，随着数据隐私保护法规的日益严格，如何在保护用户隐私的前提下有效利用数据成为一个重要挑战。</p>

            <h4>5.2 算法可解释性</h4>
            <p>深度学习模型通常被称为"黑盒"，其决策过程缺乏透明度和可解释性。在医疗、金融、司法等对决策可靠性要求极高的领域，算法的不可解释性成为了技术应用的重要障碍。</p>

            <h4>5.3 计算资源需求</h4>
            <p>大型AI模型的训练和推理需要巨大的计算资源。GPT-3的训练成本据估计超过1200万美元，这样的成本只有少数大型科技公司能够承担，可能导致AI技术的垄断化趋势。</p>

            <h4>5.4 伦理与社会影响</h4>
            <p>AI技术的快速发展带来了诸多伦理和社会问题，包括就业替代、算法偏见、自主武器系统等。如何确保AI技术的发展符合人类的整体利益，是一个需要全社会共同思考和解决的问题。</p>

            <h3>6. 未来发展趋势</h3>

            <h4>6.1 通用人工智能（AGI）</h4>
            <p>通用人工智能是AI研究的终极目标，指的是能够在各种认知任务上达到或超越人类水平的AI系统。虽然目前的AI系统在特定任务上表现优异，但距离真正的通用智能还有很长的路要走。</p>

            <h4>6.2 多模态AI</h4>
            <p>未来的AI系统将能够同时处理文本、图像、音频、视频等多种模态的信息，实现更加自然和智能的人机交互。OpenAI的GPT-4V和Google的Gemini等多模态大模型已经展现出了这一趋势。</p>

            <h4>6.3 边缘AI</h4>
            <p>随着物联网设备的普及和5G技术的发展，将AI计算能力部署到边缘设备上成为一个重要趋势。边缘AI能够减少延迟、保护隐私、降低带宽需求，为实时AI应用提供支持。</p>

            <h4>6.4 可持续AI</h4>
            <p>随着对AI环境影响的关注增加，开发更加节能高效的AI算法和硬件成为重要方向。绿色AI、小样本学习、模型压缩等技术将帮助降低AI系统的能耗和碳足迹。</p>

            <h3>7. 结论</h3>
            <p>人工智能技术正处于快速发展的关键时期，其影响力已经渗透到社会的各个层面。虽然面临着诸多挑战和限制，但AI技术的发展前景依然光明。未来，我们需要在推动技术进步的同时，更加关注AI技术的伦理、安全和可持续发展问题，确保AI技术能够真正造福人类社会。</p>

            <p>作为一项变革性技术，人工智能将继续重塑我们的世界。只有通过跨学科合作、国际协调和负责任的创新，我们才能充分发挥AI技术的潜力，创造一个更加智能、高效和公平的未来。</p>

            <div class="highlight">
                <strong>展望未来：</strong>人工智能技术的发展将是一个长期的过程，需要技术创新、政策引导、伦理规范和社会适应的协同推进。我们有理由相信，在各方共同努力下，AI技术将为人类带来更加美好的未来。
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 20px; color: #666;">
        <p>🔧 这是一个测试页面，用于验证智能网页总结助手预览功能的修复效果</p>
        <p>请使用Chrome扩展对本页面进行总结和Markdown提取测试</p>
    </div>
</body>
</html>
