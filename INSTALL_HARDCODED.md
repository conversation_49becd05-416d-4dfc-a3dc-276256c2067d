# 智能网页总结助手 - 硬编码API版本安装指南

## 📋 修改说明

本版本已将API密钥直接硬编码到扩展代码中，无需依赖外部配置文件。主要修改包括：

### ✅ 已完成的修改

1. **移除配置文件依赖**
   - 删除了 `utils/config-reader.js` 文件
   - 移除了所有从 `docs/env.md` 读取配置的逻辑

2. **硬编码API配置**
   - 在 `background/background.js` 中硬编码API密钥和端点
   - 在 `options/options.js` 中硬编码API配置显示

3. **更新用户界面**
   - 修改设置页面提示信息，移除配置文件相关说明
   - 更新错误提示信息

4. **API配置信息**
   - API密钥: `sk-466900693bb54313bb9c9a5feb986eb4`
   - API端点: `https://dashscope.aliyuncs.com/compatible-mode/v1`
   - 模型: `qwen-plus`

## 🚀 安装步骤

### 1. 准备扩展文件
确保所有修改后的文件都在扩展目录中：
```
├── manifest.json
├── background/background.js
├── options/options.html
├── options/options.js
├── sidebar/sidebar.html
├── sidebar/sidebar.js
├── content/content.js
├── utils/api.js
├── utils/storage.js
└── ... (其他文件)
```

### 2. 在Chrome中加载扩展

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展所在的文件夹
6. 确认扩展已成功加载

### 3. 验证安装

1. **检查扩展图标**
   - 在浏览器工具栏中应该能看到扩展图标

2. **测试设置页面**
   - 右键点击扩展图标 → 选项
   - 或访问 `chrome://extensions/` → 找到扩展 → 详细信息 → 扩展程序选项
   - 确认API配置显示正确（密钥应显示为格式化形式）

3. **测试功能**
   - 打开任意网页（建议使用提供的 `test-extension.html`）
   - 点击扩展图标打开侧边栏
   - 测试"开始总结"和"提取Markdown"功能

## 🧪 功能测试

### 测试页面
使用提供的测试页面验证功能：
- `test-hardcoded-api.html` - 测试API配置和连接
- `test-extension.html` - 测试扩展完整功能

### 测试步骤

1. **API连接测试**
   ```bash
   # 在浏览器中打开
   file:///path/to/test-hardcoded-api.html
   
   # 点击"测试API连接"按钮
   # 应该显示"API连接成功"
   ```

2. **总结功能测试**
   ```bash
   # 打开测试页面
   file:///path/to/test-extension.html
   
   # 点击扩展图标打开侧边栏
   # 点击"开始总结"按钮
   # 应该能成功生成内容总结
   ```

3. **Markdown提取测试**
   ```bash
   # 在测试页面的侧边栏中
   # 点击"提取Markdown"按钮
   # 应该能成功提取并显示Markdown格式内容
   ```

## ⚠️ 注意事项

1. **API密钥安全**
   - API密钥已硬编码在代码中，请确保不要将代码分享给未授权人员
   - 如需更换API密钥，需要修改代码中的硬编码值

2. **扩展更新**
   - 修改代码后需要在 `chrome://extensions/` 中点击刷新按钮
   - 或者重新加载扩展

3. **错误排查**
   - 如果功能不正常，请检查浏览器控制台是否有错误信息
   - 确认网络连接正常，能够访问API端点

## 🔧 故障排除

### 常见问题

1. **扩展无法加载**
   - 检查manifest.json语法是否正确
   - 确认所有引用的文件都存在

2. **API调用失败**
   - 检查网络连接
   - 确认API密钥和端点配置正确
   - 查看浏览器控制台错误信息

3. **侧边栏无法打开**
   - 确认在manifest.json中正确配置了side_panel
   - 检查sidebar.html文件是否存在

4. **设置页面显示异常**
   - 确认options.html和options.js文件完整
   - 检查是否有JavaScript错误

### 调试方法

1. **查看扩展日志**
   ```bash
   # 在chrome://extensions/中
   # 点击扩展的"详细信息"
   # 点击"检查视图"中的相关链接
   ```

2. **查看控制台错误**
   ```bash
   # 按F12打开开发者工具
   # 查看Console标签页中的错误信息
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. Chrome版本是否支持Manifest V3
2. 所有文件是否完整
3. 网络是否能正常访问API端点
4. 浏览器控制台是否有错误信息

---

**版本信息**
- 扩展版本: 1.0.0
- API提供商: 阿里云通义千问
- 最后更新: 2024年7月24日
