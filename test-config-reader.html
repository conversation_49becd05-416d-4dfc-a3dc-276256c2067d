<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置读取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[readonly] {
            background: #333;
            color: #ccc;
            border: 1px dashed #666;
            padding: 8px;
            width: 100%;
            box-sizing: border-box;
        }
        .config-info {
            color: #64ffda;
            font-size: 12px;
            margin-top: 5px;
        }
        button {
            background: #64ffda;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4fd3b8;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2e7d32; }
        .error { background: #d32f2f; }
        .warning { background: #f57c00; }
    </style>
</head>
<body>
    <h1>配置读取测试</h1>
    
    <div class="test-section">
        <h2>模拟配置数据</h2>
        <div class="form-group">
            <label for="testApiKey">API密钥</label>
            <input type="password" id="testApiKey" readonly placeholder="从配置文件加载中...">
            <div class="config-info">API密钥已从配置文件 docs/env.md 中预设，无需手动配置</div>
        </div>
        
        <div class="form-group">
            <label for="testBaseUrl">API端点</label>
            <input type="url" id="testBaseUrl" readonly placeholder="从配置文件加载中...">
            <div class="config-info">API端点已从配置文件 docs/env.md 中预设，无需手动配置</div>
        </div>
        
        <button onclick="loadTestConfig()">加载测试配置</button>
        <button onclick="toggleApiKey()">切换密钥显示</button>
        <button onclick="testValidation()">测试验证</button>
    </div>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // 模拟配置数据
        const mockConfig = {
            apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4',
            baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
        };
        
        let isShowingFullKey = false;
        
        function formatApiKeyForDisplay(apiKey) {
            if (!apiKey || apiKey.length < 8) {
                return apiKey;
            }
            
            const start = apiKey.substring(0, 4);
            const end = apiKey.substring(apiKey.length - 4);
            const middle = '*'.repeat(Math.max(8, apiKey.length - 8));
            
            return `${start}${middle}${end}`;
        }
        
        function loadTestConfig() {
            const apiKeyInput = document.getElementById('testApiKey');
            const baseUrlInput = document.getElementById('testBaseUrl');
            
            // 显示格式化的API密钥
            apiKeyInput.value = formatApiKeyForDisplay(mockConfig.apiKey);
            baseUrlInput.value = mockConfig.baseUrl;
            
            showResult('配置加载成功', 'success');
        }
        
        function toggleApiKey() {
            const apiKeyInput = document.getElementById('testApiKey');
            
            if (isShowingFullKey) {
                // 显示格式化密钥
                apiKeyInput.value = formatApiKeyForDisplay(mockConfig.apiKey);
                apiKeyInput.type = 'password';
                isShowingFullKey = false;
                showResult('已隐藏完整密钥', 'success');
            } else {
                // 显示完整密钥
                apiKeyInput.value = mockConfig.apiKey;
                apiKeyInput.type = 'text';
                isShowingFullKey = true;
                showResult('已显示完整密钥', 'success');
            }
        }
        
        function testValidation() {
            const errors = [];
            
            if (!mockConfig.apiKey || mockConfig.apiKey.trim() === '') {
                errors.push('API密钥不能为空');
            }
            
            if (!mockConfig.baseUrl || mockConfig.baseUrl.trim() === '') {
                errors.push('API端点不能为空');
            } else {
                try {
                    new URL(mockConfig.baseUrl);
                } catch (e) {
                    errors.push('API端点URL格式无效');
                }
            }
            
            if (errors.length === 0) {
                showResult('配置验证通过', 'success');
            } else {
                showResult('配置验证失败: ' + errors.join(', '), 'error');
            }
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultElement);
            
            // 自动滚动到最新结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 页面加载时自动加载配置
        window.addEventListener('load', () => {
            showResult('页面加载完成，准备测试配置读取功能', 'success');
        });
    </script>
</body>
</html>
