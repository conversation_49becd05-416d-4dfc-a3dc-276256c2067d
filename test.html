<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 智能网页总结助手</title>
</head>
<body>
    <h1>测试页面</h1>
    <p>这是一个测试页面，用于验证智能网页总结助手的功能。</p>
    
    <h2>主要内容</h2>
    <p>这里包含一些测试内容，用于验证扩展的总结和Markdown提取功能是否正常工作。</p>
    
    <h3>技术要点</h3>
    <ul>
        <li>修复了API调用中的字符编码问题</li>
        <li>清理了可能导致ISO-8859-1编码错误的控制字符</li>
        <li>确保API密钥只包含ASCII字符</li>
    </ul>
    
    <h3>测试步骤</h3>
    <ol>
        <li>打开这个测试页面</li>
        <li>点击扩展图标或使用快捷键</li>
        <li>选择总结或Markdown提取功能</li>
        <li>验证是否不再出现编码错误</li>
    </ol>
    
    <p>如果修复成功，应该能够正常调用AI API进行内容处理，而不会出现"String contains non ISO-8859-1 code point"错误。</p>
    
    <script>
        // 测试脚本
        console.log('测试页面已加载');
        
        // 模拟一些可能包含特殊字符的内容
        const testContent = "这是一个包含中文字符的测试内容：你好世界！";
        console.log('测试内容:', testContent);
    </script>
</body>
</html>
