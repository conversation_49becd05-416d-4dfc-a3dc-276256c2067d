<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2e7d32; }
        .error { background: #d32f2f; }
        .warning { background: #f57c00; }
        .info { background: #1976d2; }
        button {
            background: #64ffda;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4fd3b8;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .response-display {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>API连接测试</h1>
    
    <div class="test-section">
        <h2>通义千问API连接测试</h2>
        <p>使用配置文件中的API密钥测试与通义千问API的连接</p>
        
        <div class="status info">
            <strong>API密钥:</strong> sk-466900693bb54313bb9c9a5feb986eb4<br>
            <strong>API端点:</strong> https://dashscope.aliyuncs.com/compatible-mode/v1
        </div>
        
        <button id="testBtn" onclick="testApiConnection()">测试API连接</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
        
        <div id="responseDisplay" class="response-display" style="display: none;">
            <strong>API响应:</strong><br>
            <span id="responseContent"></span>
        </div>
    </div>

    <script>
        const apiConfig = {
            apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4',
            baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
            model: 'qwen-plus'
        };
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultElement);
            
            // 自动滚动到最新结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showResponse(response) {
            const responseDisplay = document.getElementById('responseDisplay');
            const responseContent = document.getElementById('responseContent');
            responseContent.textContent = JSON.stringify(response, null, 2);
            responseDisplay.style.display = 'block';
        }
        
        async function testApiConnection() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            
            showResult('开始测试API连接...', 'info');
            
            try {
                const requestBody = {
                    model: apiConfig.model,
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的AI助手。'
                        },
                        {
                            role: 'user',
                            content: '请回复"连接测试成功"'
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 50
                };
                
                showResult('发送API请求...', 'info');
                
                const response = await fetch(`${apiConfig.baseUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiConfig.apiKey}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                showResult(`收到HTTP响应: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                    
                    try {
                        const errorData = JSON.parse(errorText);
                        if (errorData.error && errorData.error.message) {
                            errorMessage = errorData.error.message;
                        } else if (errorData.message) {
                            errorMessage = errorData.message;
                        }
                        showResponse(errorData);
                    } catch (e) {
                        showResponse({ error: errorText });
                    }
                    
                    throw new Error(errorMessage);
                }
                
                const data = await response.json();
                showResponse(data);
                
                // 处理响应
                let responseContent = '';
                if (data.choices && data.choices.length > 0) {
                    responseContent = data.choices[0].message.content;
                } else if (data.output && data.output.choices && data.output.choices.length > 0) {
                    responseContent = data.output.choices[0].message.content;
                } else {
                    throw new Error('API响应格式错误');
                }
                
                showResult(`API响应内容: ${responseContent}`, 'info');
                
                if (responseContent.includes('连接测试成功')) {
                    showResult('✅ API连接测试成功！', 'success');
                } else {
                    showResult('⚠️ API连接正常，但响应内容异常', 'warning');
                }
                
            } catch (error) {
                console.error('API测试失败:', error);
                showResult(`❌ API连接测试失败: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '测试API连接';
            }
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('responseDisplay').style.display = 'none';
        }
        
        // 页面加载时显示配置信息
        window.addEventListener('load', () => {
            showResult('页面加载完成，准备测试API连接', 'info');
        });
    </script>
</body>
</html>
