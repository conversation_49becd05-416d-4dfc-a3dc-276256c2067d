<!DOCTYPE html>
<html>
<head>
    <title>生成扩展图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            margin: 10px 5px;
            padding: 8px 16px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <h1>智能网页总结助手 - 图标生成器</h1>
    <p>点击下载按钮保存对应尺寸的图标文件</p>
    
    <div class="icon-container">
        <div class="icon-item">
            <h3>16x16</h3>
            <canvas id="icon16" width="16" height="16"></canvas>
            <br>
            <button onclick="downloadIcon('icon16', 'icon16.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <h3>32x32</h3>
            <canvas id="icon32" width="32" height="32"></canvas>
            <br>
            <button onclick="downloadIcon('icon32', 'icon32.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <h3>48x48</h3>
            <canvas id="icon48" width="48" height="48"></canvas>
            <br>
            <button onclick="downloadIcon('icon48', 'icon48.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <h3>128x128</h3>
            <canvas id="icon128" width="128" height="128"></canvas>
            <br>
            <button onclick="downloadIcon('icon128', 'icon128.png')">下载</button>
        </div>
    </div>

    <script>
        // 绘制图标的函数
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128; // 基于128x128的比例缩放
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 4*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制文档图标
            const docWidth = 24 * scale;
            const docHeight = 32 * scale;
            const docX = (size - docWidth) / 2;
            const docY = (size - docHeight) / 2 - 4*scale;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(docX, docY, docWidth, docHeight);
            
            // 绘制文档线条
            const lineHeight = 1.5 * scale;
            const lineSpacing = 4 * scale;
            const lineMargin = 2 * scale;
            
            const accentGradient = ctx.createLinearGradient(0, 0, size, size);
            accentGradient.addColorStop(0, '#4facfe');
            accentGradient.addColorStop(1, '#00f2fe');
            ctx.fillStyle = accentGradient;
            
            for (let i = 0; i < 5; i++) {
                const lineY = docY + lineMargin + (i + 1) * lineSpacing;
                const lineWidth = (16 - i * 2) * scale;
                ctx.fillRect(docX + lineMargin, lineY, lineWidth, lineHeight);
            }
            
            // 绘制AI符号
            if (size >= 32) {
                const aiSize = 8 * scale;
                const aiX = size - aiSize - 8*scale;
                const aiY = size - aiSize - 8*scale;
                
                // AI圆形背景
                ctx.fillStyle = accentGradient;
                ctx.beginPath();
                ctx.arc(aiX + aiSize/2, aiY + aiSize/2, aiSize/2, 0, 2 * Math.PI);
                ctx.fill();
                
                // AI符号
                ctx.strokeStyle = 'white';
                ctx.lineWidth = Math.max(1, scale);
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                
                const centerX = aiX + aiSize/2;
                const centerY = aiY + aiSize/2;
                const arrowSize = 2 * scale;
                
                ctx.beginPath();
                ctx.moveTo(centerX - arrowSize, centerY - arrowSize/2);
                ctx.lineTo(centerX, centerY + arrowSize/2);
                ctx.lineTo(centerX + arrowSize, centerY - arrowSize/2);
                ctx.stroke();
                
                // 中心点
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(centerX, centerY, scale, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
        
        // 下载图标
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 初始化所有图标
        function initIcons() {
            drawIcon(document.getElementById('icon16'), 16);
            drawIcon(document.getElementById('icon32'), 32);
            drawIcon(document.getElementById('icon48'), 48);
            drawIcon(document.getElementById('icon128'), 128);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', initIcons);
    </script>
</body>
</html>
