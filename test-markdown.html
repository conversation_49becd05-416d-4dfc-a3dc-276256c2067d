<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown提取功能测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Monaco', 'Men<PERSON>', monospace;
            overflow-x: auto;
        }
        .feature-list {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能网页总结助手 - Markdown提取功能测试</h1>
        
        <div class="highlight">
            <strong>测试说明：</strong> 本页面用于测试Chrome扩展的Markdown提取功能。请打开扩展侧边栏，点击"提取Markdown"按钮进行测试。
        </div>

        <h2>功能概述</h2>
        <p>智能网页总结助手现已支持<strong>一键Markdown提取</strong>功能，能够将网页内容智能转换为标准的Markdown格式。</p>

        <h3>核心特性</h3>
        <div class="feature-list">
            <ul>
                <li><strong>AI智能提取</strong>：使用qwen-plus模型进行内容分析和转换</li>
                <li><strong>实时预览</strong>：提供Markdown内容的可视化预览</li>
                <li><strong>分块处理</strong>：自动处理长文章，避免API限制</li>
                <li><strong>多种操作</strong>：支持复制到剪贴板、下载为.md文件</li>
                <li><strong>编辑功能</strong>：支持预览和源码编辑模式切换</li>
            </ul>
        </div>

        <h3>技术实现</h3>
        <p>本功能基于以下技术栈实现：</p>
        
        <h4>前端技术</h4>
        <ul>
            <li>Chrome Extension Manifest V3</li>
            <li>原生JavaScript ES6+</li>
            <li>CSS3 动画和响应式设计</li>
            <li>自定义Markdown解析器</li>
        </ul>

        <h4>后端集成</h4>
        <ul>
            <li>通义千问 qwen-plus 模型</li>
            <li>OpenAI兼容API接口</li>
            <li>智能内容分块算法</li>
            <li>错误重试机制</li>
        </ul>

        <h2>使用流程</h2>
        <ol>
            <li><strong>打开扩展</strong>：点击浏览器工具栏中的扩展图标</li>
            <li><strong>选择功能</strong>：在侧边栏中点击"提取Markdown"按钮</li>
            <li><strong>等待处理</strong>：AI将分析页面内容并转换为Markdown格式</li>
            <li><strong>预览确认</strong>：查看转换后的Markdown预览效果</li>
            <li><strong>执行操作</strong>：复制到剪贴板或下载为文件</li>
        </ol>

        <h3>代码示例</h3>
        <p>以下是一个简单的JavaScript代码示例：</p>
        <div class="code-block">
// 示例：调用Markdown提取API
async function extractMarkdown(content) {
    const response = await chrome.runtime.sendMessage({
        action: 'extractMarkdown',
        content: content,
        url: window.location.href,
        title: document.title
    });
    
    if (response.success) {
        console.log('Markdown提取成功:', response.markdown);
        return response.markdown;
    } else {
        throw new Error(response.error);
    }
}
        </div>

        <h2>性能优化</h2>
        <p>为了确保良好的用户体验，我们实现了以下优化措施：</p>

        <blockquote>
            <p><strong>智能分块：</strong> 对于超过6000字符的长文章，系统会自动分块处理，避免API token限制。</p>
        </blockquote>

        <blockquote>
            <p><strong>进度指示：</strong> 提供详细的处理进度反馈，包括"分析页面结构"、"提取主要内容"、"AI处理中"等阶段。</p>
        </blockquote>

        <blockquote>
            <p><strong>取消操作：</strong> 用户可以随时取消正在进行的提取操作。</p>
        </blockquote>

        <h3>错误处理</h3>
        <p>系统具备完善的错误处理机制：</p>
        <ul>
            <li>API密钥验证</li>
            <li>网络连接检测</li>
            <li>内容长度检查</li>
            <li>自动重试机制</li>
            <li>友好的错误提示</li>
        </ul>

        <h2>兼容性说明</h2>
        <p>本功能与现有的网页总结功能完全兼容，不会影响原有功能的正常使用。用户可以在同一个扩展中同时使用：</p>
        <ul>
            <li>智能网页总结</li>
            <li>Markdown内容提取</li>
            <li>多种导出格式</li>
            <li>历史记录管理</li>
        </ul>

        <div class="highlight">
            <strong>提示：</strong> 请确保已在扩展设置中配置了有效的API密钥，否则无法使用AI功能。
        </div>

        <h2>更新日志</h2>
        <h3>v1.1.0 - Markdown提取功能</h3>
        <ul>
            <li>✨ 新增一键Markdown提取功能</li>
            <li>🎨 优化用户界面和交互体验</li>
            <li>⚡ 实现智能分块处理算法</li>
            <li>🔧 添加实时预览和编辑功能</li>
            <li>🛡️ 增强错误处理和用户反馈</li>
        </ul>

        <p><em>感谢使用智能网页总结助手！如有问题或建议，欢迎反馈。</em></p>
    </div>
</body>
</html>
