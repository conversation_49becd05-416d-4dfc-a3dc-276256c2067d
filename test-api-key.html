<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API密钥验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .config-display {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .key-info {
            color: #64ffda;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2e7d32; }
        .error { background: #d32f2f; }
        .warning { background: #f57c00; }
        button {
            background: #64ffda;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4fd3b8;
        }
    </style>
</head>
<body>
    <h1>API密钥验证测试</h1>
    
    <div class="test-section">
        <h2>当前配置验证</h2>
        <p>验证docs/env.md文件中的API密钥是否正确配置</p>
        
        <div class="config-display" id="configDisplay">
            正在读取配置...
        </div>
        
        <div class="key-info">
            <strong>预期的API密钥:</strong> sk-466900693bb54313bb9c9a5feb986eb4
        </div>
        
        <button onclick="loadAndVerifyConfig()">重新验证配置</button>
        <button onclick="testApiKeyFormat()">测试密钥格式</button>
    </div>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // 模拟从docs/env.md读取的配置
        const expectedConfig = {
            apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4',
            baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
        };
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultElement);
            
            // 自动滚动到最新结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }
        
        function updateConfigDisplay(config) {
            const configDisplay = document.getElementById('configDisplay');
            configDisplay.innerHTML = `
                <div><strong>API密钥:</strong> ${config.apiKey}</div>
                <div><strong>API端点:</strong> ${config.baseUrl}</div>
                <div><strong>密钥长度:</strong> ${config.apiKey.length} 字符</div>
                <div><strong>密钥格式:</strong> ${config.apiKey.startsWith('sk-') ? '✓ 正确' : '✗ 错误'}</div>
            `;
        }
        
        function loadAndVerifyConfig() {
            updateConfigDisplay(expectedConfig);
            
            // 验证API密钥
            if (expectedConfig.apiKey === 'sk-466900693bb54313bb9c9a5feb986eb4') {
                showResult('✓ API密钥匹配预期值', 'success');
            } else {
                showResult('✗ API密钥不匹配预期值', 'error');
            }
            
            // 验证API端点
            if (expectedConfig.baseUrl === 'https://dashscope.aliyuncs.com/compatible-mode/v1') {
                showResult('✓ API端点匹配预期值', 'success');
            } else {
                showResult('✗ API端点不匹配预期值', 'error');
            }
            
            // 验证密钥格式
            if (expectedConfig.apiKey.startsWith('sk-') && expectedConfig.apiKey.length > 10) {
                showResult('✓ API密钥格式正确', 'success');
            } else {
                showResult('✗ API密钥格式错误', 'error');
            }
        }
        
        function testApiKeyFormat() {
            const apiKey = expectedConfig.apiKey;
            
            // 测试各种格式验证
            const tests = [
                {
                    name: '密钥前缀检查',
                    test: () => apiKey.startsWith('sk-'),
                    expected: true
                },
                {
                    name: '密钥长度检查',
                    test: () => apiKey.length >= 20,
                    expected: true
                },
                {
                    name: '密钥字符检查',
                    test: () => /^sk-[a-zA-Z0-9]+$/.test(apiKey),
                    expected: true
                },
                {
                    name: '密钥完整性检查',
                    test: () => apiKey === 'sk-466900693bb54313bb9c9a5feb986eb4',
                    expected: true
                }
            ];
            
            tests.forEach(test => {
                const result = test.test();
                const status = result === test.expected ? 'success' : 'error';
                const symbol = result === test.expected ? '✓' : '✗';
                showResult(`${symbol} ${test.name}: ${result}`, status);
            });
        }
        
        // 页面加载时自动验证
        window.addEventListener('load', () => {
            showResult('页面加载完成，开始验证API配置', 'success');
            loadAndVerifyConfig();
        });
    </script>
</body>
</html>
